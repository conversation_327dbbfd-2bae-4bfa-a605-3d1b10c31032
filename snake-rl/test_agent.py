#!/usr/bin/env python3
"""
Test script for the enhanced agent
"""

def test_config():
    """Test config loading"""
    try:
        from config import Config
        print("✓ Config loaded successfully")
        print(f"  Enhanced state: {Config.USE_ENHANCED_STATE}")
        print(f"  Model version: {Config.MODEL_VERSION}")
        print(f"  Episodes: {Config.EPISODES}")
        return True
    except Exception as e:
        print(f"✗ Config loading failed: {e}")
        return False

def test_agent_basic():
    """Test basic agent creation"""
    try:
        from agent import RLAgent
        print("✓ Agent module imported")
        
        agent = RLAgent(state_size=(10, 10), action_size=3)
        print("✓ Agent created successfully")
        print(f"  Q-table shape: {agent.q_table.shape}")
        print(f"  Enhanced state: {agent.use_enhanced_state}")
        print(f"  Prioritized replay: {agent.use_prioritized_replay}")
        return True
    except Exception as e:
        print(f"✗ Agent creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_snake_game():
    """Test snake game"""
    try:
        from snake_game import SnakeGame
        print("✓ SnakeGame imported")
        
        env = SnakeGame()
        print("✓ SnakeGame created")
        
        state = env.reset()
        print(f"✓ Game reset, state shape: {state.shape}")
        
        next_state, reward, done, info = env.step(0)
        print(f"✓ Game step executed, reward: {reward}")
        return True
    except Exception as e:
        print(f"✗ SnakeGame test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training():
    """Test training module"""
    try:
        from training import Trainer
        print("✓ Training module imported")
        
        trainer = Trainer(load_model=False)
        print("✓ Trainer created")
        return True
    except Exception as e:
        print(f"✗ Training test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== Testing Enhanced Snake RL ===")
    
    tests = [
        ("Config", test_config),
        ("SnakeGame", test_snake_game),
        ("Agent", test_agent_basic),
        ("Training", test_training),
    ]
    
    passed = 0
    for name, test_func in tests:
        print(f"\n--- Testing {name} ---")
        if test_func():
            passed += 1
        else:
            print(f"Stopping tests due to {name} failure")
            break
    
    print(f"\n=== Results: {passed}/{len(tests)} tests passed ===")
