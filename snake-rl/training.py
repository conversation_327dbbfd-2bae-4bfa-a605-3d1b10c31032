from collections import deque
import numpy as np
from snake_game import <PERSON><PERSON><PERSON>
from agent import <PERSON><PERSON><PERSON>
from typing import Dict, List
import time
import os

class Trainer:
    """Handles training of RL agent on Snake Game"""

    def __init__(self, load_model=False, extended_training=False, keep_memory=1.0, use_color=False):
        from config import Config
        self.config = Config

        # Initialize environment
        self.env = SnakeGame()

        # Initialize agent
        self.agent = RLAgent(state_size=(Config.GRID_SIZE, Config.GRID_SIZE), action_size=3)

        # Enhanced model loading with version support
        model_loaded = False
        if load_model:
            # Try to load enhanced model first
            if os.path.exists(Config.MODEL_ENHANCED_PATH):
                model_loaded = self.agent.load(Config.MODEL_ENHANCED_PATH)
                print(f"Loaded enhanced model v{Config.MODEL_VERSION}")
            # Fallback to final model
            elif os.path.exists(Config.MODEL_FINAL_PATH):
                model_loaded = self.agent.load(Config.MODEL_FINAL_PATH)
                print(f"Loaded final model")
            # Fallback to legacy model
            elif os.path.exists(Config.MODEL_LEGACY_PATH):
                model_loaded = self.agent.load(Config.MODEL_LEGACY_PATH)
                print(f"Loaded legacy model")

            if model_loaded:
                print(f"Memory kept: {keep_memory*100:.0f}%")
                # Control memory retention
                if keep_memory < 1.0:
                    current_size = len(self.agent.memory)
                    new_size = int(current_size * keep_memory)
                    self.agent.memory = deque(list(self.agent.memory)[-new_size:], maxlen=Config.MEMORY_SIZE)
            else:
                print("No existing model found, starting fresh")

        # Training parameters from config with validation
        self.episodes = max(1, Config.EPISODES)
        self.batch_size = max(1, Config.BATCH_SIZE)
        self.save_interval = max(1, Config.SAVE_INTERVAL)
        self.render_interval = max(1, Config.RENDER_INTERVAL)

        # Store color preference
        self.use_color = use_color

        # Extended training adjustments
        if extended_training:
            self.episodes = max(1, Config.EPISODES * 5)  # 5x longer training
            self.agent.learning_rate = max(0.0001, Config.LEARNING_RATE / 2)  # Slower learning

        # Enhanced logging and metrics
        self.scores: List[int] = []
        self.epsilons: List[float] = []
        self.losses: List[float] = []
        self.detailed_metrics = {
            'episode_lengths': [],
            'rewards_per_episode': [],
            'q_value_means': [],
            'exploration_efficiency': []
        }

        # Early stopping
        self.best_avg_score = -float('inf')
        self.episodes_without_improvement = 0

        # Curriculum learning
        self.current_curriculum_stage = 0
        self.curriculum_episode_count = 0

        # Create models directory
        os.makedirs(Config.MODEL_DIR, exist_ok=True)

    def train(self):
        """Enhanced main training loop with curriculum learning and early stopping"""
        print("Starting enhanced training...")
        print(f"Enhanced state representation: {self.agent.use_enhanced_state}")
        print(f"Prioritized replay: {self.agent.use_prioritized_replay}")
        start_time = time.time()

        for e in range(self.episodes):
            # Apply curriculum learning
            if self.config.USE_CURRICULUM_LEARNING:
                self._apply_curriculum_stage(e)

            state = self.env.reset()
            total_reward = 0
            episode_steps = 0
            done = False

            while not done:
                # Get action from agent
                action = self.agent.act(state)

                # Take step in environment
                next_state, reward, done, info = self.env.step(action)

                # Store experience
                self.agent.remember(state, action, reward, next_state, done)

                # Train on batch
                loss = self.agent.replay(self.batch_size)
                if loss > 0:
                    self.losses.append(loss)

                total_reward += reward
                episode_steps += 1
                state = next_state

                # Optional rendering (skip if interval is 0)
                if self.render_interval > 0 and e % self.render_interval == 0:
                    self.env.render(use_color=self.use_color)
                    time.sleep(0.1)

            # Log episode results
            self.scores.append(self.env.score)
            self.epsilons.append(self.agent.epsilon)

            # Enhanced metrics tracking
            if self.config.TRACK_DETAILED_METRICS:
                self.detailed_metrics['episode_lengths'].append(episode_steps)
                self.detailed_metrics['rewards_per_episode'].append(total_reward)

                if self.agent.training_metrics['q_values']:
                    avg_q = np.mean(self.agent.training_metrics['q_values'][-episode_steps:])
                    self.detailed_metrics['q_value_means'].append(avg_q)

            # Print progress with enhanced information
            if e % 10 == 0:
                avg_score = np.mean(self.scores[-self.config.METRICS_WINDOW:])
                avg_steps = np.mean(self.detailed_metrics['episode_lengths'][-10:]) if self.detailed_metrics['episode_lengths'] else 0

                print(f"Episode {e}/{self.episodes} | "
                      f"Score: {self.env.score} | "
                      f"Avg Score: {avg_score:.1f} | "
                      f"Steps: {episode_steps} | "
                      f"Avg Steps: {avg_steps:.1f} | "
                      f"Epsilon: {self.agent.epsilon:.3f} | "
                      f"LR: {self.agent.learning_rate:.6f}")

                # Memory status
                if e % 100 == 0:
                    print(f"Memory: {len(self.agent.memory)}/{self.config.MEMORY_SIZE} | "
                          f"Loss: {np.mean(self.losses[-10:]):.4f}" if self.losses else "No loss")

            # Early stopping check
            if self.config.USE_EARLY_STOPPING and e > self.config.METRICS_WINDOW:
                current_avg = np.mean(self.scores[-self.config.METRICS_WINDOW:])
                if current_avg > self.best_avg_score + self.config.EARLY_STOP_MIN_DELTA:
                    self.best_avg_score = current_avg
                    self.episodes_without_improvement = 0
                else:
                    self.episodes_without_improvement += 1

                if self.episodes_without_improvement >= self.config.EARLY_STOP_PATIENCE:
                    print(f"Early stopping at episode {e} (no improvement for {self.config.EARLY_STOP_PATIENCE} episodes)")
                    break

            # Save model periodically with enhanced backup system
            if e % self.save_interval == 0 and e > 0:
                self._save_checkpoint(e)

            # Auto backup at intervals
            if self.config.AUTO_BACKUP and e % self.config.BACKUP_INTERVAL == 0 and e > 0:
                backup_path = f"{self.config.MODEL_DIR}/snake_agent_backup_{e}.npy"
                self.agent.save(backup_path)
                self._cleanup_old_backups()

        # Save final enhanced model
        final_path = self.config.MODEL_ENHANCED_PATH
        self.agent.save(final_path)

        # Also save as final model for compatibility
        self.agent.save(self.config.MODEL_FINAL_PATH)

        print(f"Enhanced training completed in {time.time() - start_time:.1f} seconds")
        print(f"Final model saved to {final_path}")

        # Print final statistics
        if self.scores:
            print(f"Final average score: {np.mean(self.scores[-100:]):.2f}")
            print(f"Best score: {max(self.scores)}")
            print(f"Total episodes: {len(self.scores)}")

        return {
            'scores': self.scores,
            'epsilons': self.epsilons,
            'losses': self.losses,
            'detailed_metrics': self.detailed_metrics
        }

    def _apply_curriculum_stage(self, episode: int):
        """Apply curriculum learning stages"""
        total_episodes = 0
        for i, stage in enumerate(self.config.CURRICULUM_STAGES):
            if episode < total_episodes + stage['episodes']:
                if i != self.current_curriculum_stage:
                    self.current_curriculum_stage = i
                    print(f"Switching to curriculum stage {i+1}: {stage}")

                # Apply stage settings
                self.env.max_steps = stage['max_steps']
                # Note: food_spawn_bias would need to be implemented in SnakeGame
                break
            total_episodes += stage['episodes']

    def _save_checkpoint(self, episode: int):
        """Save training checkpoint with cleanup"""
        # Remove previous checkpoint
        prev_checkpoint = f"{self.config.MODEL_DIR}/snake_agent_{episode-self.save_interval}.npy"
        if os.path.exists(prev_checkpoint):
            os.remove(prev_checkpoint)

        # Save new checkpoint
        checkpoint_path = f"{self.config.MODEL_DIR}/snake_agent_{episode}.npy"
        self.agent.save(checkpoint_path)

    def _cleanup_old_backups(self):
        """Clean up old backup files, keeping only the most recent ones"""
        backup_files = []
        for f in os.listdir(self.config.MODEL_DIR):
            if f.startswith("snake_agent_backup_") and f.endswith(".npy"):
                backup_files.append(f)

        # Sort by episode number (extracted from filename)
        backup_files.sort(key=lambda x: int(x.split('_')[-1].split('.')[0]))

        # Remove old backups, keep only MAX_BACKUPS
        while len(backup_files) > self.config.MAX_BACKUPS:
            old_backup = backup_files.pop(0)
            backup_path = os.path.join(self.config.MODEL_DIR, old_backup)
            if os.path.exists(backup_path):
                os.remove(backup_path)

if __name__ == "__main__":
    import argparse
    from config import Config

    parser = argparse.ArgumentParser(description='Train Snake RL Agent')
    parser.add_argument('--load', action='store_true', help='Load existing model')
    parser.add_argument('--extended', action='store_true', help='Use extended training')
    parser.add_argument('--keep_memory', type=float, default=Config.DEFAULT_MEMORY_RETENTION,
                      help='Fraction of memory to keep when loading (0.0-1.0)')
    parser.add_argument('--color', action='store_true',
                      help='Enable colored output')
    args = parser.parse_args()

    trainer = Trainer(
        load_model=args.load,
        extended_training=args.extended,
        keep_memory=args.keep_memory,
        use_color=args.color
    )
    trainer.train()
