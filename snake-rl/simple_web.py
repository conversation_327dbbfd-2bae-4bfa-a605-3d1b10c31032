#!/usr/bin/env python3
"""
Simple web app for testing
"""

from flask import Flask, jsonify
import os
import sys

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

app = Flask(__name__)

@app.route('/')
def index():
    return "Snake RL Web App - Simple Version"

@app.route('/api/test')
def test():
    try:
        from config import Config
        return jsonify({
            'status': 'ok',
            'enhanced_state': Config.USE_ENHANCED_STATE,
            'model_version': Config.MODEL_VERSION
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/agent_test')
def agent_test():
    try:
        from agent import RLAgent
        agent = RLAgent(state_size=(10, 10), action_size=3)
        return jsonify({
            'status': 'ok',
            'q_table_shape': list(agent.q_table.shape),
            'enhanced_state': agent.use_enhanced_state
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/model_test')
def model_test():
    try:
        from agent import RLAgent
        agent = RLAgent(state_size=(10, 10), action_size=3)
        
        model_path = "models/snake_agent_v2.npy"
        if os.path.exists(model_path):
            success = agent.load(model_path)
            return jsonify({
                'status': 'ok',
                'model_loaded': success,
                'model_path': model_path,
                'memory_size': len(agent.memory),
                'epsilon': agent.epsilon
            })
        else:
            return jsonify({
                'status': 'ok',
                'model_loaded': False,
                'model_path': model_path,
                'message': 'No model file found'
            })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("Starting simple web app...")
    app.run(host='0.0.0.0', port=5001, debug=True)
