#!/usr/bin/env python3
"""
Clean, simple web app for Snake RL
"""

from flask import Flask, jsonify, send_from_directory
from threading import Thread, Event
import numpy as np
import os
import time
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)

# Import game components
from snake_game import <PERSON><PERSON><PERSON>
from agent import RLAgent
from evaluation import Evaluator
from config import Config

app = Flask(__name__, static_folder='web/static', static_url_path='')

# Global variables
trainer = None
training_thread = None
stop_training = Event()

class SimpleTrainer:
    def __init__(self):
        self.env = SnakeGame()
        self.agent = RLAgent(state_size=(10, 10), action_size=3)
        
        # Try to load existing model
        self.model_path = "models/snake_agent_v2.npy"
        if os.path.exists(self.model_path):
            if self.agent.load(self.model_path):
                logging.info(f"Loaded model from {self.model_path}")
            else:
                logging.warning("Failed to load model, starting fresh")
        else:
            logging.info("No existing model, starting fresh")
        
        # Training stats
        self.episode = 0
        self.scores = []
        self.is_training = False
    
    def train(self):
        """Simple training loop"""
        self.is_training = True
        logging.info("Starting training...")
        
        while not stop_training.is_set() and self.episode < 200:  # Shorter training
            state = self.env.reset()
            done = False
            
            while not done and not stop_training.is_set():
                action = self.agent.act(state)
                next_state, reward, done, info = self.env.step(action)
                self.agent.remember(state, action, reward, next_state, done)
                
                if len(self.agent.memory) > Config.BATCH_SIZE:
                    self.agent.replay(Config.BATCH_SIZE)
                
                state = next_state
            
            self.scores.append(self.env.score)
            self.episode += 1
            
            # Save periodically
            if self.episode % 50 == 0:
                self.agent.save(self.model_path)
                logging.info(f"Episode {self.episode}, Score: {self.env.score}")
        
        # Final save
        self.agent.save(self.model_path)
        self.is_training = False
        logging.info(f"Training completed. Episodes: {self.episode}")

def get_trainer():
    global trainer
    if trainer is None:
        trainer = SimpleTrainer()
    return trainer

@app.route('/')
def index():
    return send_from_directory('web/static', 'index.html')

@app.route('/app.js')
def app_js():
    return send_from_directory('web/static', 'app.js')

@app.route('/api/status')
def status():
    trainer = get_trainer()
    return jsonify({
        'status': 'ok',
        'model_loaded': os.path.exists(trainer.model_path),
        'q_table_shape': list(trainer.agent.q_table.shape),
        'memory_size': len(trainer.agent.memory),
        'epsilon': float(trainer.agent.epsilon),
        'enhanced_state': trainer.agent.use_enhanced_state
    })

@app.route('/api/training/status')
def training_status():
    trainer = get_trainer()
    global training_thread
    
    return jsonify({
        'running': training_thread is not None and training_thread.is_alive(),
        'episode': trainer.episode,
        'score': trainer.scores[-1] if trainer.scores else 0,
        'avg_score': float(np.mean(trainer.scores[-10:])) if trainer.scores else 0,
        'epsilon': float(trainer.agent.epsilon),
        'memory_size': len(trainer.agent.memory)
    })

@app.route('/api/training/start', methods=['POST'])
def start_training():
    global training_thread
    trainer = get_trainer()
    
    if training_thread is None or not training_thread.is_alive():
        stop_training.clear()
        training_thread = Thread(target=trainer.train)
        training_thread.start()
        return jsonify({'status': 'training started'})
    else:
        return jsonify({'status': 'training already running'})

@app.route('/api/training/stop', methods=['POST'])
def stop_training_endpoint():
    stop_training.set()
    return jsonify({'status': 'training stopped'})

@app.route('/api/evaluation')
def evaluation():
    trainer = get_trainer()
    
    if not os.path.exists(trainer.model_path):
        return jsonify({'error': 'No model available'}), 404
    
    try:
        evaluator = Evaluator(trainer.model_path)
        results = evaluator.evaluate(num_episodes=5, render=False)
        
        return jsonify({
            'mean_score': float(results['mean_score']),
            'max_score': int(results['max_score']),
            'mean_steps': float(results['mean_steps']),
            'action_distribution': {
                'left': float(results['action_distribution']['left']),
                'right': float(results['action_distribution']['right']),
                'straight': float(results['action_distribution']['straight'])
            }
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/test')
def test():
    return jsonify({
        'message': 'Snake RL API is working!',
        'config': {
            'enhanced_state': Config.USE_ENHANCED_STATE,
            'prioritized_replay': Config.USE_PRIORITIZED_REPLAY,
            'episodes': Config.EPISODES,
            'batch_size': Config.BATCH_SIZE
        }
    })

if __name__ == '__main__':
    # Create models directory
    os.makedirs('models', exist_ok=True)
    
    print("Starting Snake RL Web App...")
    print("Access at: http://localhost:5000")
    
    app.run(host='0.0.0.0', port=5000, debug=False)
