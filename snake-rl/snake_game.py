import numpy as np
from typing import <PERSON><PERSON>, List

class SnakeGame:
    """Snake Game Environment for Reinforcement Learning"""

    def __init__(self):
        from config import Config
        # Game parameters
        self.grid_size = Config.GRID_SIZE
        self.max_steps = Config.MAX_STEPS
        self.current_step = 0

        # Game state
        self.snake: List[Tuple[int, int]] = []
        self.direction: Tuple[int, int] = (0, 1)  # Initial direction: right
        self.food: Tuple[int, int] = (0, 0)
        self.score = 0
        self.game_over = False

        # Initialize game
        self.reset()

    def reset(self) -> np.ndarray:
        """Reset the game to initial state"""
        self.current_step = 0
        self.score = 0
        self.game_over = False

        # Start snake in center
        center = self.grid_size // 2
        self.snake = [(center, center)]
        self.direction = (0, 1)  # Right

        # Place initial food
        self._place_food()

        return self._get_state()

    def _place_food(self):
        """Place food in random empty cell"""
        empty_cells = []
        for i in range(self.grid_size):
            for j in range(self.grid_size):
                if (i, j) not in self.snake:
                    empty_cells.append((i, j))

        if empty_cells:
            self.food = empty_cells[np.random.choice(len(empty_cells))]

    def _get_state(self) -> np.ndarray:
        """Get current game state as numpy array"""
        state = np.zeros((self.grid_size, self.grid_size), dtype=np.float32)

        # Mark snake body (0.5 for body, 1.0 for head)
        for i, (x, y) in enumerate(self.snake):
            state[x, y] = 0.5 if i < len(self.snake) - 1 else 1.0

        # Mark food
        state[self.food] = -1.0

        return state

    def step(self, action: int) -> Tuple[np.ndarray, float, bool, dict]:
        """Execute one game step with enhanced reward system"""
        from config import Config

        if self.game_over:
            return self._get_state(), 0, True, {'score': self.score}

        self.current_step += 1

        # Store previous position for reward calculation
        head = self.snake[-1]
        old_dist = abs(head[0] - self.food[0]) + abs(head[1] - self.food[1])

        # Update direction based on action
        self._update_direction(action)

        # Move snake
        new_head = (head[0] + self.direction[0], head[1] + self.direction[1])

        # Check for collisions
        if (new_head[0] < 0 or new_head[0] >= self.grid_size or
            new_head[1] < 0 or new_head[1] >= self.grid_size or
            new_head in self.snake):
            self.game_over = True
            return self._get_state(), Config.REWARD_DEATH, True, {'score': self.score}

        # Add new head
        self.snake.append(new_head)

        # Initialize reward with step penalty
        reward = Config.REWARD_STEP

        # Check if food eaten
        if new_head == self.food:
            self.score += 1

            # Enhanced food reward based on snake length
            if Config.USE_DYNAMIC_REWARDS:
                food_reward = Config.REWARD_FOOD + (len(self.snake) * Config.REWARD_LENGTH_MULTIPLIER)
            else:
                food_reward = Config.REWARD_FOOD

            reward = food_reward
            self._place_food()
        else:
            # Remove tail if no food eaten
            self.snake.pop(0)

            # Add survival bonus for longer snakes
            if Config.USE_DYNAMIC_REWARDS:
                reward += Config.REWARD_SURVIVAL * len(self.snake)

        # Enhanced distance-based rewards
        if not self.game_over and Config.USE_DYNAMIC_REWARDS:
            new_dist = abs(new_head[0] - self.food[0]) + abs(new_head[1] - self.food[1])

            if new_dist < old_dist:
                # Moving closer - bonus increases with snake length
                reward += Config.REWARD_CLOSER * (1 + len(self.snake) * 0.1)
            elif new_dist > old_dist:
                # Moving away - penalty
                reward += Config.REWARD_FARTHER

            # Efficiency bonus for direct paths
            if new_dist <= old_dist - 2:  # Significant improvement
                reward += Config.REWARD_EFFICIENCY

        # Check if game won (snake fills entire grid)
        if len(self.snake) == self.grid_size * self.grid_size:
            self.game_over = True
            reward += Config.REWARD_WIN

        # Check step limit
        if self.current_step >= self.max_steps:
            self.game_over = True

        return self._get_state(), reward, self.game_over, {
            'score': self.score,
            'steps': self.current_step,
            'snake_length': len(self.snake)
        }

    def _update_direction(self, action: int):
        """Update snake direction based on action"""
        # Directions: right, down, left, up
        directions = [(0, 1), (1, 0), (0, -1), (-1, 0)]
        current_idx = directions.index(self.direction)

        if action == 0:  # Left turn
            new_idx = (current_idx - 1) % 4
        elif action == 1:  # Right turn
            new_idx = (current_idx + 1) % 4
        else:  # Straight (action == 2)
            new_idx = current_idx

        self.direction = directions[new_idx]

    def render(self, use_color=False):
        """Render game state in CLI with optional color"""
        if use_color:
            # Colored version
            COLORS = {
                'reset': '\033[0m',
                'snake_head': '\033[92m', # Green
                'snake_body': '\033[32m', # Dark Green
                'food': '\033[91m',       # Red
                'empty': '\033[90m',      # Dark Gray
                'text': '\033[93m'       # Yellow
            }

            grid = []
            for i in range(self.grid_size):
                row = []
                for j in range(self.grid_size):
                    if (i, j) == self.snake[-1]:  # Head
                        row.append(f"{COLORS['snake_head']}@")
                    elif (i, j) in self.snake:    # Body
                        row.append(f"{COLORS['snake_body']}O")
                    elif (i, j) == self.food:     # Food
                        row.append(f"{COLORS['food']}*")
                    else:                         # Empty
                        row.append(f"{COLORS['empty']}.")
                grid.append(' '.join(row) + COLORS['reset'])

            print(f"{COLORS['text']}Score: {self.score} | Steps: {self.current_step}/{self.max_steps}{COLORS['reset']}")
            for row in grid:
                print(row)
            print()
        else:
            # Simple ASCII version
            grid = [['.' for _ in range(self.grid_size)] for _ in range(self.grid_size)]

            # Mark snake
            for i, (x, y) in enumerate(self.snake):
                grid[x][y] = 'O' if i < len(self.snake) - 1 else '@'  # Head

            # Mark food
            grid[self.food[0]][self.food[1]] = '*'

            # Print grid
            print(f"Score: {self.score} | Steps: {self.current_step}/{self.max_steps}")
            for row in grid:
                print(' '.join(row))
            print()
