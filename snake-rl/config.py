# Enhanced Reinforcement Learning Configuration
class Config:
    # ===== TRAINING PARAMETERS =====
    EPISODES = 1000  # Training episodes per session
    BATCH_SIZE = 32
    SAVE_INTERVAL = 100
    RENDER_INTERVAL = 500
    DEFAULT_MEMORY_RETENTION = 0.7  # Keep 70% newest experiences when loading

    # ===== AGENT HYPERPARAMETERS =====
    # Q-Learning Parameters
    GAMMA = 0.99  # Discount factor
    EPSILON_START = 1.0  # Initial exploration rate
    EPSILON_MIN = 0.01   # Minimum exploration rate
    EPSILON_DECAY = 0.995  # Exploration decay rate (improved)
    LEARNING_RATE = 0.001  # Q-learning rate (increased)
    MEMORY_SIZE = 10000    # Experience buffer size (doubled)

    # Learning Rate Scheduling
    USE_LR_SCHEDULING = True
    LR_DECAY_RATE = 0.99
    LR_DECAY_INTERVAL = 100  # Episodes between decay
    LR_MIN = 0.0001

    # ===== STATE REPRESENTATION =====
    # Use standard state representation (stable and efficient)
    USE_ENHANCED_STATE = False
    STATE_FEATURES = {
        'head_position': True,      # (x, y) coordinates
        'food_direction': True,     # Relative food direction (8 directions)
        'food_distance': True,      # Manhattan distance to food (normalized)
        'danger_detection': True,   # Multi-level danger detection
        'body_proximity': True,     # Body segments near head
        'wall_proximity': True,     # Distance to walls
        'tail_direction': True,     # Direction to tail
        'snake_length': True,       # Current snake length
    }

    # Enhanced Danger Detection
    DANGER_LEVELS = 4  # 0=safe, 1=near_wall, 2=near_body, 3=immediate_collision
    PROXIMITY_RADIUS = 2  # Check danger within this radius

    # ===== REWARD SYSTEM OPTIMIZATION =====
    # Base Rewards
    REWARD_FOOD = 100           # Increased food reward
    REWARD_DEATH = -100         # Increased death penalty
    REWARD_STEP = -1            # Increased step penalty to encourage efficiency
    REWARD_WIN = 1000           # Winning reward

    # Dynamic Rewards
    USE_DYNAMIC_REWARDS = True
    REWARD_CLOSER = 2           # Moving closer to food
    REWARD_FARTHER = -2         # Moving away from food
    REWARD_EFFICIENCY = 5       # Bonus for efficient paths
    REWARD_SURVIVAL = 0.1       # Small survival bonus per step

    # Length-based Rewards
    REWARD_LENGTH_MULTIPLIER = 2  # Multiply food reward by snake length

    # ===== EXPERIENCE REPLAY =====
    USE_PRIORITIZED_REPLAY = False  # Use standard replay for stability
    PRIORITY_ALPHA = 0.6        # Prioritization exponent
    PRIORITY_BETA_START = 0.4   # Importance sampling start
    PRIORITY_BETA_END = 1.0     # Importance sampling end
    PRIORITY_EPSILON = 1e-6     # Small constant for numerical stability

    # ===== ENVIRONMENT SETTINGS =====
    GRID_SIZE = 10
    MAX_STEPS = 200  # Increased to allow longer games

    # ===== MODEL MANAGEMENT =====
    # Model Versioning
    MODEL_VERSION = "v2"  # Version for enhanced models
    MODEL_COMPATIBILITY_CHECK = True

    # Model Paths
    MODEL_DIR = "models"
    MODEL_LEGACY_PATH = "models/snake_agent.npy"
    MODEL_ENHANCED_PATH = "models/snake_agent_v2.npy"
    MODEL_FINAL_PATH = "models/snake_agent_final.npy"

    # Backup Settings
    AUTO_BACKUP = True
    BACKUP_INTERVAL = 500  # Episodes between backups
    MAX_BACKUPS = 5

    # ===== TRAINING OPTIMIZATION =====
    # Curriculum Learning
    USE_CURRICULUM_LEARNING = True
    CURRICULUM_STAGES = [
        {'episodes': 200, 'max_steps': 50, 'food_spawn_bias': 0.3},   # Easy: food near head
        {'episodes': 300, 'max_steps': 100, 'food_spawn_bias': 0.1},  # Medium: slight bias
        {'episodes': 500, 'max_steps': 200, 'food_spawn_bias': 0.0},  # Hard: random spawn
    ]

    # Early Stopping
    USE_EARLY_STOPPING = True
    EARLY_STOP_PATIENCE = 100  # Episodes without improvement
    EARLY_STOP_MIN_DELTA = 0.1  # Minimum improvement threshold

    # Performance Tracking
    TRACK_DETAILED_METRICS = True
    METRICS_WINDOW = 100  # Episodes for moving averages