import time  # Explicit import
import numpy as np
from snake_game import <PERSON><PERSON><PERSON>
from agent import <PERSON><PERSON><PERSON>
from typing import Dict, List, Any
import matplotlib.pyplot as plt
import os
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Evaluator:
    """Evaluates trained RL agent performance"""

    def __init__(self, model_path: str):
        from config import Config
        self.config = Config
        self.env = SnakeGame()
        self.agent = RLAgent(state_size=(10, 10), action_size=3)

        # Try to load the model
        if self.agent.load(model_path):
            logger.info(f"Successfully loaded model from {model_path}")
        else:
            logger.warning(f"Failed to load model from {model_path}, using fresh agent")

        self.agent.epsilon = 0.01  # Minimal exploration for evaluation

    def evaluate(self, num_episodes: int = 100, render: bool = False) -> Dict[str, Any]:
        """Run evaluation episodes with enhanced metrics"""
        scores = []
        steps = []
        action_counts = [0, 0, 0]  # left, right, straight
        game_states = []  # For replay

        for e in range(num_episodes):
            state = self.env.reset()
            episode_states = [state.copy()]
            done = False
            episode_steps = 0

            while not done:
                action = self.agent.act(state)
                action_counts[action] += 1
                state, _, done, info = self.env.step(action)
                episode_states.append(state.copy())
                episode_steps += 1

                if render:
                    self.env.render()
                    time.sleep(0.1)

            scores.append(info['score'])
            steps.append(episode_steps)
            game_states.append(episode_states)

            # Store last episode states for replay (convert to lists for JSON serialization)
            if e == num_episodes - 1:
                self.last_episode_states = [state.tolist() for state in episode_states]

        # Calculate action percentages
        total_actions = sum(action_counts)
        action_percentages = [round(count/total_actions*100, 1) for count in action_counts]

        return {
            'mean_score': float(np.mean(scores)),
            'max_score': float(np.max(scores)),
            'min_score': float(np.min(scores)),
            'mean_steps': float(np.mean(steps)),
            'score_std': float(np.std(scores)),
            'steps_std': float(np.std(steps)),
            'action_distribution': {
                'left': action_percentages[0],
                'right': action_percentages[1],
                'straight': action_percentages[2]
            },
            'scores': scores,
            'steps': steps,
            'game_states': game_states
        }

    def show_model_info(self, episode_count: int = None):
        """Display information about the loaded model"""
        print("\nModel Information:")
        if episode_count is not None:
            print(f"- Episode: {episode_count} | Memory size: {len(self.agent.memory)} experiences")
        print(f"- Exploration rate (epsilon): {self.agent.epsilon:.4f}")
        print(f"- Learning rate: {self.agent.learning_rate:.6f}")
        print(f"- Discount factor (gamma): {self.agent.gamma:.4f}")
        if episode_count is None:
            print(f"- Memory size: {len(self.agent.memory)} experiences")

    # Removed replay_game functionality per user request

    def plot_results(self, results: Dict[str, float], save_path: str = None):
        """Visualize evaluation metrics"""
        plt.figure(figsize=(12, 5))

        # Score distribution
        plt.subplot(1, 2, 1)
        plt.hist(results['scores'], bins=20, edgecolor='black')
        plt.title('Score Distribution')
        plt.xlabel('Score')
        plt.ylabel('Frequency')

        # Steps vs Score
        plt.subplot(1, 2, 2)
        plt.scatter(results['steps'], results['scores'], alpha=0.6)
        plt.title('Steps vs Score')
        plt.xlabel('Steps')
        plt.ylabel('Score')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path)
        else:
            plt.show()

if __name__ == "__main__":
    # Enhanced evaluation
    num_episodes = 50
    evaluator = Evaluator("models/snake_agent_final.npy")
    results = evaluator.evaluate(num_episodes=num_episodes, render=False)

    # Display results
    print("\n=== Evaluation Results ===")
    print(f"Mean Score: {results['mean_score']:.1f} ± {results['score_std']:.1f}")
    print(f"Max Score: {results['max_score']}")
    print(f"Steps per Episode: {results['mean_steps']:.1f} ± {results['steps_std']:.1f}")
    print("\nAction Distribution:")
    print(f"- Left: {results['action_distribution']['left']}%")
    print(f"- Right: {results['action_distribution']['right']}%")
    print(f"- Straight: {results['action_distribution']['straight']}%")

    # Show model info with episode count
    evaluator.show_model_info(episode_count=num_episodes)

    # Plot and save results
    os.makedirs("plots", exist_ok=True)
    evaluator.plot_results(results, save_path="plots/evaluation_results.png")

    # Removed replay call per user request
