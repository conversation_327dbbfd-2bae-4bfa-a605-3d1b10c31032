#!/usr/bin/env python3
"""
Fix model compatibility by creating a new compatible model
"""

import numpy as np
import os
from agent import RLAgent
from config import Config

def create_compatible_model():
    """Create a new model compatible with current config"""
    print("Creating compatible model...")
    
    # Create fresh agent with current config
    agent = RLAgent(state_size=(10, 10), action_size=3)
    print(f"Created agent with enhanced state: {agent.use_enhanced_state}")
    print(f"Q-table shape: {agent.q_table.shape}")
    
    # Try to load existing model data (memory, epsilon, etc.) but not Q-table
    if os.path.exists("models/snake_agent_v2.npy"):
        try:
            data = np.load("models/snake_agent_v2.npy", allow_pickle=True).item()
            print("Loading experience data from existing model...")
            
            # Load only compatible data
            if 'memory' in data:
                agent.memory.extend(data['memory'][-5000:])  # Keep last 5000 experiences
                print(f"Loaded {len(agent.memory)} experiences")
            
            if 'epsilon' in data:
                agent.epsilon = max(data['epsilon'], Config.EPSILON_MIN)
                print(f"Loaded epsilon: {agent.epsilon}")
                
            if 'episodes_trained' in data:
                agent.episodes_trained = data['episodes_trained']
                print(f"Episodes trained: {agent.episodes_trained}")
                
        except Exception as e:
            print(f"Could not load existing data: {e}")
    
    # Save new compatible model
    model_path = "models/snake_agent_v2.npy"
    agent.save(model_path)
    print(f"Saved compatible model to {model_path}")
    
    # Verify the model can be loaded
    test_agent = RLAgent(state_size=(10, 10), action_size=3)
    if test_agent.load(model_path):
        print("✓ Model verification successful")
        print(f"  Q-table shape: {test_agent.q_table.shape}")
        print(f"  Enhanced state: {test_agent.use_enhanced_state}")
        print(f"  Memory size: {len(test_agent.memory)}")
        return True
    else:
        print("✗ Model verification failed")
        return False

if __name__ == "__main__":
    success = create_compatible_model()
    if success:
        print("\n✅ Model compatibility fixed!")
        print("You can now start the web app without errors.")
    else:
        print("\n❌ Failed to fix model compatibility.")
