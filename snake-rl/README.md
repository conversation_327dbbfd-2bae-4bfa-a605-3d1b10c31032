# Snake Reinforcement Learning Project

A complete snake game implementation with enhanced Q-Learning reinforcement learning and web interface for training and evaluation.

## 🎯 Key Features

- **Enhanced Q-Learning Agent**: Optimized Q-table with improved state representation and training algorithms
- **Advanced Training System**: Curriculum learning, early stopping, and learning rate scheduling
- **Interactive Web Dashboard**: Real-time training monitoring, model evaluation, and training control
- **Robust Model Management**: Automatic saving, loading, versioning, and backup system
- **Optimized Performance**: Improved reward system and exploration strategies
- **REST API**: Clean and reliable communication with comprehensive endpoints
- **Docker Support**: Easy deployment with single container setup

## 🚀 Performance Improvements

This enhanced version includes significant improvements over the original:

- **Better Learning**: Enhanced reward system with dynamic bonuses
- **Smarter Exploration**: Balanced action selection and optimized epsilon decay
- **Curriculum Learning**: Progressive difficulty stages for better training
- **Early Stopping**: Prevents overtraining and saves computational resources
- **Model Versioning**: Backward compatibility with automatic model migration
- **Increased Memory**: 10,000 experience buffer (2x larger)
- **Training Continuity**: Seamless training continuation after completion

## System Architecture

- **Backend**: Flask app serving both API and static files
- **Frontend**: Vanilla JavaScript with automatic status updates
- **Training**: Q-Learning agent with experience replay
- **Storage**: Models saved as NumPy files for persistence

## 🏁 Quick Start

### With Docker (Recommended)
```bash
cd /opt/snake-rl
docker compose up
```
Access web interface at `http://localhost:5000`

### Local Development
```bash
cd snake-rl
source venv/bin/activate
pip install flask numpy matplotlib
python web_clean.py  # Enhanced clean version
# OR
python web/app.py    # Original version
```
Access web interface at `http://localhost:5000`

### Command Line Training
```bash
cd snake-rl
source venv/bin/activate
python clean_training.py  # Clean training script
# OR
python training.py         # Enhanced training with all features
```

## 📱 Usage

### Enhanced Web Interface
1. **Start Training**: Click "Start Training" to begin or continue training
2. **Stop Training**: Click "Stop Training" to pause training at any time
3. **Reset Training**: Click "Reset Training" to start fresh from episode 0
4. **Monitor Progress**: Real-time updates every 2 seconds showing:
   - Current episode and progress percentage
   - Latest score and 100-episode average
   - Exploration rate (epsilon)
   - Memory usage and training status
5. **Evaluate Model**: Click "Evaluate Model" to test the trained agent
6. **View Results**: Evaluation shows mean/max scores, steps, and action distribution

### Enhanced Training Behavior
- **Smart Loading**: Automatically detects and loads the best available model (v2 → final → legacy)
- **Continuous Training**: Training continues beyond initial episode limit
- **Auto-Save**: Models saved every 50 episodes with automatic backup system
- **Early Stopping**: Training stops automatically when no improvement is detected
- **Curriculum Learning**: Progressive difficulty stages for optimal learning
- **Model Versioning**: Enhanced models (v2) with backward compatibility

## 📁 Project Structure

```
snake-rl/
├── web/                   # Original web interface
│   ├── app.py             # Original Flask backend
│   └── static/            # HTML, CSS, JavaScript
├── web_clean.py           # Enhanced clean web app (recommended)
├── snake_game.py          # Core game logic with enhanced rewards
├── agent.py               # Enhanced Q-Learning agent
├── training.py            # Enhanced training with all features
├── clean_training.py      # Simple, reliable training script
├── evaluation.py          # Model evaluation
├── config.py              # Centralized configuration (enhanced)
├── fix_model_compatibility.py  # Model migration utility
├── test_agent.py          # Testing utilities
├── docker-compose.yml     # Docker configuration
├── requirements.txt       # Python dependencies
└── models/                # Saved models (auto-created)
    ├── snake_agent_v2.npy      # Enhanced model (current)
    ├── snake_agent_final.npy   # Final model (compatibility)
    └── snake_agent_legacy.npy  # Legacy model (backup)
```

## ⚙️ Enhanced Configuration

All parameters are centralized in `config.py` for easy tuning:

### Training Parameters
```python
EPISODES = 1000           # Training episodes per session
BATCH_SIZE = 32           # Experience replay batch size
LEARNING_RATE = 0.001     # Q-learning rate (enhanced)
MEMORY_SIZE = 10000       # Experience buffer size (doubled)
EPSILON_START = 1.0       # Initial exploration rate
EPSILON_MIN = 0.01        # Minimum exploration rate
EPSILON_DECAY = 0.995     # Exploration decay rate (optimized)
```

### Enhanced Features
```python
# Learning Rate Scheduling
USE_LR_SCHEDULING = True
LR_DECAY_RATE = 0.99
LR_MIN = 0.0001

# Curriculum Learning
USE_CURRICULUM_LEARNING = True
CURRICULUM_STAGES = [
    {'episodes': 200, 'max_steps': 50},   # Easy
    {'episodes': 300, 'max_steps': 100},  # Medium
    {'episodes': 500, 'max_steps': 200},  # Hard
]

# Early Stopping
USE_EARLY_STOPPING = True
EARLY_STOP_PATIENCE = 100

# Enhanced Rewards
USE_DYNAMIC_REWARDS = True
REWARD_FOOD = 100           # Increased food reward
REWARD_DEATH = -100         # Increased death penalty
REWARD_EFFICIENCY = 5       # Bonus for efficient paths
```

## 🌐 Enhanced API Endpoints

### Training Control
- `GET /` - Web interface
- `POST /api/training/start` - Start training
- `POST /api/training/stop` - Stop training
- `POST /api/training/reset` - Reset training to episode 0
- `GET /api/training/status` - Get detailed training status

### Model Evaluation
- `GET /api/final_evaluation` - Evaluate trained model
- `GET /api/evaluation` - Alternative evaluation endpoint
- `GET /api/status` - System status and configuration

### Utilities
- `GET /api/test` - API health check

## 🔬 Technical Details

### Enhanced State Representation
- **Current**: Optimized 5D Q-table (10×10×4×3×3 = 3,600 states)
- **Available**: Enhanced 8D representation for advanced features
- **Action Space**: 3 actions (turn left, turn right, go straight)

### Improved Reward System
- **Food Reward**: +100 (increased from +10)
- **Death Penalty**: -100 (increased from -10)
- **Step Penalty**: -1 (encourages efficiency)
- **Dynamic Bonuses**: Efficiency, survival, and length-based rewards
- **Proximity Rewards**: +2 for moving closer to food, -2 for moving away

### Enhanced Memory & Learning
- **Experience Buffer**: 10,000 transitions (doubled)
- **Batch Training**: 32 experiences per update
- **Learning Rate**: Adaptive with scheduling (0.001 → 0.0001)
- **Exploration**: Balanced epsilon decay (0.995) with action balancing
- **Persistence**: Full model state including Q-table, memory, and training metrics

### Advanced Features
- **Curriculum Learning**: 3-stage progressive difficulty
- **Early Stopping**: Automatic training termination when no improvement
- **Model Versioning**: Backward compatibility with automatic migration
- **Backup System**: Automatic model backups every 500 episodes
- **Performance Tracking**: Detailed metrics and loss monitoring

## 📊 Performance Results

### Training Improvements
- **Score Achievement**: Consistently reaching scores of 2+ (vs. 0-1 in original)
- **Training Stability**: No crashes or memory issues during extended training
- **Learning Efficiency**: Early stopping prevents overtraining
- **Memory Utilization**: Full 10,000 experience buffer utilization

### Typical Training Session
```
Episode 50   | Score: 2 | Avg: 0.8 | Epsilon: 0.60
Episode 100  | Score: 2 | Avg: 1.1 | Epsilon: 0.37
Episode 200  | Score: 1 | Avg: 1.2 | Epsilon: 0.13
Episode 300  | Score: 2 | Avg: 1.4 | Epsilon: 0.05
Training completed with early stopping
```

## 🛠️ Troubleshooting

### Common Issues

**Training won't start after completion**
```bash
# Reset training to start fresh
curl -X POST http://localhost:5000/api/training/reset
```

**Model compatibility errors**
```bash
# Fix model compatibility
python fix_model_compatibility.py
```

**Web interface not loading**
```bash
# Check if web app is running
curl http://localhost:5000/api/status
# Restart web app
python web_clean.py
```

### Development Tips
- Use `web_clean.py` for the most stable experience
- Check logs for detailed training information
- Models are automatically backed up - check `models/` directory
- Use `clean_training.py` for command-line training without web interface

## 🎯 Future Enhancements

Potential improvements for advanced users:
- **Enhanced State Representation**: 8D state space (currently disabled for stability)
- **Prioritized Experience Replay**: Advanced sampling strategy
- **Double DQN**: Reduced overestimation bias
- **Dueling DQN**: Separate value and advantage estimation
- **Live Game Visualization**: Real-time snake game rendering
- **Multi-Agent Training**: Competitive snake environments
