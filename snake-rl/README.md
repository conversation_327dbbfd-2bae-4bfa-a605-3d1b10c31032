# Snake Reinforcement Learning Project

A complete snake game implementation with Q-Learning reinforcement learning and web interface for training and evaluation.

## Key Features

- **Q-Learning Agent**: Optimized Q-table with simplified state representation
- **Web Dashboard**: Real-time training monitoring and model evaluation
- **Persistent Training**: Models are automatically saved and loaded for continued training
- **REST API**: Simple and reliable communication without WebSocket complexity
- **Docker Support**: Easy deployment with single container setup

## System Architecture

- **Backend**: Flask app serving both API and static files
- **Frontend**: Vanilla JavaScript with automatic status updates
- **Training**: Q-Learning agent with experience replay
- **Storage**: Models saved as NumPy files for persistence

## Quick Start

### With Docker (Recommended)
```bash
cd snake-rl
docker compose up
```
Access web interface at `http://localhost`

### Local Development
```bash
cd snake-rl
source venv/bin/activate
pip install flask flask-socketio numpy matplotlib
python web/app.py
```
Access web interface at `http://localhost:5000`

## Usage

### Web Interface
1. **Start Training**: Click "Start Training" to begin or continue training
2. **Monitor Progress**: Real-time updates every 2 seconds showing:
   - Current episode and progress percentage
   - Latest score and 100-episode average
   - Exploration rate (epsilon)
3. **Evaluate Model**: Click "Evaluate Model" to test the trained agent
4. **View Results**: Evaluation shows mean/max scores and average steps

### Training Behavior
- **First Run**: Creates new model and starts learning from scratch
- **Subsequent Runs**: Automatically loads existing model and continues training
- **Model Persistence**: All progress is saved in `models/snake_agent.npy`

## Project Structure

```
snake-rl/
├── web/                   # Web interface
│   ├── app.py             # Flask backend with API routes
│   └── static/            # HTML, CSS, JavaScript
├── snake_game.py          # Core game logic
├── agent.py               # Q-Learning agent
├── training.py            # Standalone training script
├── evaluation.py          # Model evaluation
├── config.py              # Training parameters
├── docker-compose.yml     # Docker configuration
├── requirements.txt       # Python dependencies
└── models/                # Saved models (auto-created)
```

## Configuration

Key parameters in `config.py`:

```python
EPISODES = 1000           # Training episodes per session
BATCH_SIZE = 32           # Experience replay batch size
LEARNING_RATE = 0.001     # Q-learning rate
MEMORY_SIZE = 5000        # Experience buffer size
EPSILON_START = 1.0       # Initial exploration rate
EPSILON_MIN = 0.01        # Minimum exploration rate
EPSILON_DECAY = 0.995     # Exploration decay rate
```

## API Endpoints

- `GET /` - Web interface
- `POST /api/training/start` - Start training
- `POST /api/training/stop` - Stop training
- `GET /api/training/status` - Get training status
- `GET /api/final_evaluation` - Evaluate trained model

## Technical Details

- **State Representation**: Simplified 5D Q-table (head_x, head_y, food_direction, danger_level, action)
- **Action Space**: 3 actions (turn left, turn right, go straight)
- **Reward System**: +10 for food, -10 for collision, -1 for each step
- **Memory**: Experience replay with 5000 transitions
- **Persistence**: Full model state saved including Q-table and memory
