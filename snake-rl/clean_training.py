#!/usr/bin/env python3
"""
Clean training script - simple and reliable
"""

import numpy as np
import time
import os
from agent import RLAgent
from snake_game import <PERSON><PERSON><PERSON>
from config import Config

def train_clean_model():
    """Train a new clean model"""
    print("=== Clean Training Start ===")
    print(f"Enhanced State: {Config.USE_ENHANCED_STATE}")
    print(f"Prioritized Replay: {Config.USE_PRIORITIZED_REPLAY}")
    
    # Create environment and agent
    env = SnakeGame()
    agent = RLAgent(state_size=(10, 10), action_size=3)
    
    print(f"Q-table shape: {agent.q_table.shape}")
    print(f"Memory size: {Config.MEMORY_SIZE}")
    
    # Training parameters
    episodes = 500  # Moderate number for clean training
    scores = []
    
    start_time = time.time()
    
    for episode in range(episodes):
        state = env.reset()
        total_reward = 0
        done = False
        
        while not done:
            action = agent.act(state)
            next_state, reward, done, info = env.step(action)
            agent.remember(state, action, reward, next_state, done)
            
            # Train if enough experiences
            if len(agent.memory) > Config.BATCH_SIZE:
                loss = agent.replay(Config.BATCH_SIZE)
            
            state = next_state
            total_reward += reward
        
        scores.append(env.score)
        
        # Progress report
        if episode % 50 == 0:
            avg_score = np.mean(scores[-50:]) if scores else 0
            print(f"Episode {episode:3d} | Score: {env.score:2d} | "
                  f"Avg: {avg_score:.2f} | Epsilon: {agent.epsilon:.3f} | "
                  f"Memory: {len(agent.memory)}")
        
        # Save checkpoint
        if episode % 100 == 0 and episode > 0:
            agent.save(f"models/checkpoint_{episode}.npy")
    
    # Save final model
    os.makedirs("models", exist_ok=True)
    agent.save("models/snake_agent_v2.npy")
    agent.save("models/snake_agent_final.npy")
    
    # Final statistics
    training_time = time.time() - start_time
    final_avg = np.mean(scores[-100:]) if len(scores) >= 100 else np.mean(scores)
    
    print(f"\n=== Training Complete ===")
    print(f"Episodes: {episodes}")
    print(f"Training time: {training_time:.1f} seconds")
    print(f"Final average score: {final_avg:.2f}")
    print(f"Best score: {max(scores)}")
    print(f"Final epsilon: {agent.epsilon:.3f}")
    print(f"Memory size: {len(agent.memory)}")
    print(f"Model saved to: models/snake_agent_v2.npy")
    
    return {
        'episodes': episodes,
        'scores': scores,
        'final_avg': final_avg,
        'best_score': max(scores),
        'training_time': training_time
    }

if __name__ == "__main__":
    results = train_clean_model()
    print("\n✅ Clean training completed successfully!")
