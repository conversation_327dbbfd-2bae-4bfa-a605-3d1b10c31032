import numpy as np
from typing import Dict, Any, Tuple, List
from collections import deque
import random
import os
import logging

class RLAgent:
    """Enhanced Reinforcement Learning Agent for Snake Game"""

    def __init__(self, state_size: tuple, action_size: int):
        # Import config locally to avoid circular imports
        import config
        Config = config.Config
        self.config = Config
        self.state_size = (Config.GRID_SIZE, Config.GRID_SIZE)
        self.action_size = action_size
        self.direction = (0, 1)  # Initial direction (right)

        # Hyperparameters from config
        self.gamma = Config.GAMMA
        self.epsilon = Config.EPSILON_START
        self.epsilon_min = Config.EPSILON_MIN
        self.epsilon_decay = Config.EPSILON_DECAY
        self.learning_rate = Config.LEARNING_RATE
        self.initial_learning_rate = Config.LEARNING_RATE

        # Experience replay memory
        self.memory = deque(maxlen=Config.MEMORY_SIZE)
        # Track last actions to balance exploration
        self.last_actions = deque(maxlen=10)

        # Enhanced state representation
        self.use_enhanced_state = Config.USE_ENHANCED_STATE

        if self.use_enhanced_state:
            # Enhanced Q-table dimensions (optimized for memory):
            # head_x(10) x head_y(10) x food_dir(8) x food_dist(4) x danger(4) x body_prox(3) x wall_prox(3) x length(3)
            self.q_table = np.zeros((10, 10, 8, 4, 4, 3, 3, 3, 3), dtype=np.float16)
        else:
            # Legacy Q-table for backward compatibility
            self.q_table = np.zeros((10, 10, 4, 3, 3), dtype=np.float16)

        # Prioritized Experience Replay
        self.use_prioritized_replay = Config.USE_PRIORITIZED_REPLAY
        if self.use_prioritized_replay:
            self.priorities = deque(maxlen=Config.MEMORY_SIZE)
            self.priority_alpha = Config.PRIORITY_ALPHA
            self.priority_beta = Config.PRIORITY_BETA_START
            self.priority_beta_end = Config.PRIORITY_BETA_END
            self.priority_epsilon = Config.PRIORITY_EPSILON

        # Learning rate scheduling
        self.use_lr_scheduling = Config.USE_LR_SCHEDULING
        self.lr_decay_rate = Config.LR_DECAY_RATE
        self.lr_decay_interval = Config.LR_DECAY_INTERVAL
        self.lr_min = Config.LR_MIN
        self.episodes_trained = 0

        # Performance tracking
        self.training_metrics = {
            'losses': [],
            'q_values': [],
            'exploration_rates': [],
            'learning_rates': []
        }

    def remember(self, state: np.ndarray, action: int, reward: float, next_state: np.ndarray, done: bool):
        """Store experience in memory with optional prioritization"""
        experience = (state, action, reward, next_state, done)
        self.memory.append(experience)

        if self.use_prioritized_replay:
            # Calculate initial priority (TD error proxy)
            try:
                if len(self.memory) > 1:
                    state_features = self._get_state_features(state)
                    next_features = self._get_state_features(next_state)

                    current_q = self.q_table[tuple(state_features) + (action,)]
                    target = reward
                    if not done:
                        target += self.gamma * np.max(self.q_table[tuple(next_features)])

                    priority = abs(target - current_q) + self.priority_epsilon
                    self.priorities.append(priority)
                else:
                    self.priorities.append(1.0)  # Max priority for first experience
            except Exception as e:
                # Fallback: use default priority
                self.priorities.append(1.0)
                logging.warning(f"Error calculating priority: {e}")

        # Ensure priorities and memory stay in sync
        if self.use_prioritized_replay:
            while len(self.priorities) > len(self.memory):
                self.priorities.popleft()
            while len(self.priorities) < len(self.memory):
                self.priorities.append(1.0)

    def act(self, state: np.ndarray) -> int:
        """Select action using epsilon-greedy policy with balanced exploration"""
        if np.random.rand() <= self.epsilon:
            # Enhanced exploration with action balancing
            action = random.randrange(self.action_size)
            if len(self.last_actions) > 5:
                # Reduce probability of repeating dominant action
                action_counts = [self.last_actions.count(a) for a in range(3)]
                if max(action_counts) > len(self.last_actions) * 0.6:  # If one action dominates
                    # Choose less frequent action
                    min_count = min(action_counts)
                    action = action_counts.index(min_count)

            self.last_actions.append(action)
            return action

        # Exploitation with enhanced state features
        state_features = self._get_state_features(state)
        q_values = self.q_table[tuple(state_features)]
        action = np.argmax(q_values)

        # Track Q-values for monitoring
        if self.config.TRACK_DETAILED_METRICS:
            self.training_metrics['q_values'].append(np.max(q_values))

        self.last_actions.append(action)
        return action

    def replay(self, batch_size: int) -> float:
        """Enhanced training with prioritized experience replay"""
        if len(self.memory) < batch_size:
            return 0.0

        # Sample experiences
        if (self.use_prioritized_replay and
            len(self.priorities) >= batch_size and
            len(self.priorities) == len(self.memory)):
            try:
                minibatch, indices, weights = self._sample_prioritized_batch(batch_size)
            except Exception as e:
                logging.warning(f"Prioritized sampling failed: {e}, falling back to random sampling")
                minibatch = random.sample(self.memory, batch_size)
                indices = None
                weights = np.ones(batch_size)
        else:
            minibatch = random.sample(self.memory, batch_size)
            indices = None
            weights = np.ones(batch_size)

        total_loss = 0.0
        td_errors = []

        for i, (state, action, reward, next_state, done) in enumerate(minibatch):
            # Get state representations
            state_features = self._get_state_features(state)
            next_features = self._get_state_features(next_state)

            # Calculate target
            target = reward
            if not done:
                target += self.gamma * np.max(self.q_table[tuple(next_features)])

            # Current Q-value
            current_q = self.q_table[tuple(state_features) + (action,)]

            # TD error
            td_error = target - current_q
            td_errors.append(abs(td_error))

            # Update Q-value with importance sampling weight
            weight = weights[i] if self.use_prioritized_replay else 1.0
            self.q_table[tuple(state_features) + (action,)] += \
                self.learning_rate * weight * td_error

            total_loss += abs(td_error)

        # Update priorities if using prioritized replay
        if self.use_prioritized_replay and indices is not None:
            self._update_priorities(indices, td_errors)

        # Update learning parameters
        self._update_learning_parameters()

        # Track metrics
        if self.config.TRACK_DETAILED_METRICS:
            self.training_metrics['losses'].append(total_loss / batch_size)
            self.training_metrics['exploration_rates'].append(self.epsilon)
            self.training_metrics['learning_rates'].append(self.learning_rate)

        return total_loss / batch_size

    def _get_state_features(self, state: np.ndarray) -> tuple:
        """Get state features based on configuration"""
        if self.use_enhanced_state:
            return self._get_enhanced_state(state)
        else:
            return self._get_simplified_state(state)

    def _get_enhanced_state(self, state: np.ndarray) -> tuple:
        """Enhanced state representation with multiple features"""
        # Find positions
        head_pos = np.unravel_index(np.argmax(state == 1.0), state.shape)
        food_pos = np.unravel_index(np.argmax(state == -1.0), state.shape)

        # Convert coordinates with bounds checking
        head_x, head_y = min(max(head_pos[0], 0), 9), min(max(head_pos[1], 0), 9)
        food_x, food_y = min(max(food_pos[0], 0), 9), min(max(food_pos[1], 0), 9)

        # 1. Food direction (8 directions instead of 4 quadrants)
        food_dir = self._get_food_direction_8way(head_pos, food_pos)

        # 2. Food distance (normalized to 5 levels)
        food_dist = self._get_normalized_distance(head_pos, food_pos)

        # 3. Enhanced danger detection (4 levels)
        danger = self._get_danger_level(state, head_pos)

        # 4. Body proximity (3 levels: none, near, adjacent)
        body_prox = self._get_body_proximity(state, head_pos)

        # 5. Wall proximity (3 levels: far, near, adjacent)
        wall_prox = self._get_wall_proximity(head_pos)

        # 6. Snake length (normalized to 3 levels)
        snake_length = min(int(np.sum(state >= 0.5) / 30), 2)  # 0-2 levels

        return (head_x, head_y, food_dir, food_dist, danger, body_prox, wall_prox, snake_length)

    def _get_food_direction_8way(self, head_pos: tuple, food_pos: tuple) -> int:
        """Get food direction in 8 directions (0-7)"""
        dx = food_pos[0] - head_pos[0]
        dy = food_pos[1] - head_pos[1]

        if dx == 0 and dy > 0: return 0  # Right
        elif dx > 0 and dy > 0: return 1  # Down-Right
        elif dx > 0 and dy == 0: return 2  # Down
        elif dx > 0 and dy < 0: return 3  # Down-Left
        elif dx == 0 and dy < 0: return 4  # Left
        elif dx < 0 and dy < 0: return 5  # Up-Left
        elif dx < 0 and dy == 0: return 6  # Up
        else: return 7  # Up-Right

    def _get_normalized_distance(self, head_pos: tuple, food_pos: tuple) -> int:
        """Get normalized Manhattan distance (0-3)"""
        distance = abs(head_pos[0] - food_pos[0]) + abs(head_pos[1] - food_pos[1])
        return min(distance // 5, 3)  # 0-3 levels

    def _get_danger_level(self, state: np.ndarray, head_pos: tuple) -> int:
        """Enhanced danger detection (0-3)"""
        x, y = head_pos

        # Check immediate collision
        if self._check_immediate_danger(state, head_pos):
            return 3  # Immediate collision

        # Check near body collision
        if self._check_near_body(state, head_pos):
            return 2  # Near body

        # Check near wall
        if min(x, y, 9-x, 9-y) <= 1:
            return 1  # Near wall

        return 0  # Safe

    def _get_body_proximity(self, state: np.ndarray, head_pos: tuple) -> int:
        """Check body proximity (0-2)"""
        x, y = head_pos
        body_nearby = 0

        # Check 3x3 area around head
        for dx in [-1, 0, 1]:
            for dy in [-1, 0, 1]:
                if dx == 0 and dy == 0:
                    continue
                nx, ny = x + dx, y + dy
                if 0 <= nx < 10 and 0 <= ny < 10 and state[nx, ny] == 0.5:
                    body_nearby += 1

        return min(body_nearby // 3, 2)  # 0-2 levels

    def _get_wall_proximity(self, head_pos: tuple) -> int:
        """Get wall proximity (0-2)"""
        x, y = head_pos
        min_dist = min(x, y, 9-x, 9-y)

        if min_dist == 0: return 2  # At wall
        elif min_dist == 1: return 1  # Near wall
        else: return 0  # Far from wall

    def _check_near_body(self, state: np.ndarray, head_pos: tuple) -> bool:
        """Check if body is within proximity radius"""
        x, y = head_pos
        radius = self.config.PROXIMITY_RADIUS

        for dx in range(-radius, radius + 1):
            for dy in range(-radius, radius + 1):
                if dx == 0 and dy == 0:
                    continue
                nx, ny = x + dx, y + dy
                if 0 <= nx < 10 and 0 <= ny < 10 and state[nx, ny] == 0.5:
                    return True
        return False

    def _get_simplified_state(self, state: np.ndarray) -> tuple:
        """Legacy simplified state representation for backward compatibility"""
        # Find positions
        head_pos = np.unravel_index(np.argmax(state == 1.0), state.shape)
        food_pos = np.unravel_index(np.argmax(state == -1.0), state.shape)

        # Convert coordinates with bounds checking
        head_x, head_y = min(max(head_pos[0], 0), 9), min(max(head_pos[1], 0), 9)
        food_x, food_y = min(max(food_pos[0], 0), 9), min(max(food_pos[1], 0), 9)

        # Food direction quadrant (0-3)
        if food_x < head_x and food_y <= head_y:
            food_dir = 0  # Top-left
        elif food_x >= head_x and food_y < head_y:
            food_dir = 1  # Top-right
        elif food_x < head_x and food_y > head_y:
            food_dir = 2  # Bottom-left
        else:
            food_dir = 3  # Bottom-right

        # Danger detection (0=none, 1=near, 2=collision)
        danger = 0
        if self._check_immediate_danger(state, head_pos):
            danger = 2
        elif self._check_near_danger(state, head_pos):
            danger = 1

        return (head_x, head_y, food_dir, danger)

    def _check_immediate_danger(self, state: np.ndarray, head_pos: tuple) -> bool:
        """Check for immediate collision danger"""
        x, y = head_pos
        # Check walls
        if (self.direction == (0, 1) and y == 9) or \
           (self.direction == (0, -1) and y == 0) or \
           (self.direction == (1, 0) and x == 9) or \
           (self.direction == (-1, 0) and x == 0):
            return True
        # Check body
        next_pos = (x + self.direction[0], y + self.direction[1])
        return state[next_pos] == 0.5

    def _check_near_danger(self, state: np.ndarray, head_pos: tuple) -> bool:
        """Check for nearby danger (1 step away)"""
        x, y = head_pos
        # Check if near walls
        if (self.direction == (0, 1) and y >= 8) or \
           (self.direction == (0, -1) and y <= 1) or \
           (self.direction == (1, 0) and x >= 8) or \
           (self.direction == (-1, 0) and x <= 1):
            return True
        # Check if body is nearby
        next_pos = (x + self.direction[0], y + self.direction[1])
        return state[next_pos] == 0.5

    def _check_body_ahead(self, state: np.ndarray, head_pos: tuple) -> int:
        """Check if body is in front of head"""
        x, y = head_pos
        if self.direction == (0, 1):  # Right
            return 1 if y < 9 and state[x, y+1] == 0.5 else 0
        elif self.direction == (0, -1):  # Left
            return 1 if y > 0 and state[x, y-1] == 0.5 else 0
        elif self.direction == (1, 0):  # Down
            return 1 if x < 9 and state[x+1, y] == 0.5 else 0
        else:  # Up
            return 1 if x > 0 and state[x-1, y] == 0.5 else 0

    def _check_body_left(self, state: np.ndarray, head_pos: tuple) -> int:
        """Check if body is to the left of head"""
        x, y = head_pos
        if self.direction == (0, 1):  # Right
            return 1 if x > 0 and state[x-1, y] == 0.5 else 0
        elif self.direction == (0, -1):  # Left
            return 1 if x < 9 and state[x+1, y] == 0.5 else 0
        elif self.direction == (1, 0):  # Down
            return 1 if y > 0 and state[x, y-1] == 0.5 else 0
        else:  # Up
            return 1 if y < 9 and state[x, y+1] == 0.5 else 0

    def _check_body_right(self, state: np.ndarray, head_pos: tuple) -> int:
        """Check if body is to the right of head"""
        x, y = head_pos
        if self.direction == (0, 1):  # Right
            return 1 if x < 9 and state[x+1, y] == 0.5 else 0
        elif self.direction == (0, -1):  # Left
            return 1 if x > 0 and state[x-1, y] == 0.5 else 0
        elif self.direction == (1, 0):  # Down
            return 1 if y < 9 and state[x, y+1] == 0.5 else 0
        else:  # Up
            return 1 if y > 0 and state[x, y-1] == 0.5 else 0

    def _sample_prioritized_batch(self, batch_size: int) -> Tuple[List, List[int], np.ndarray]:
        """Sample batch using prioritized experience replay"""
        # Ensure priorities and memory have same size
        memory_size = len(self.memory)
        priorities_size = len(self.priorities)

        if priorities_size != memory_size:
            # Sync priorities with memory size
            if priorities_size < memory_size:
                # Add missing priorities with default value
                missing = memory_size - priorities_size
                for _ in range(missing):
                    self.priorities.append(1.0)
            elif priorities_size > memory_size:
                # Trim excess priorities
                while len(self.priorities) > memory_size:
                    self.priorities.popleft()

        priorities = np.array(self.priorities)
        probabilities = priorities ** self.priority_alpha
        probabilities /= probabilities.sum()

        # Sample indices
        indices = np.random.choice(len(self.memory), batch_size, p=probabilities)

        # Calculate importance sampling weights
        weights = (len(self.memory) * probabilities[indices]) ** (-self.priority_beta)
        weights /= weights.max()  # Normalize

        # Get experiences
        experiences = [self.memory[i] for i in indices]

        return experiences, indices.tolist(), weights

    def _update_priorities(self, indices: List[int], td_errors: List[float]):
        """Update priorities based on TD errors"""
        for i, td_error in zip(indices, td_errors):
            if i < len(self.priorities):
                self.priorities[i] = td_error + self.priority_epsilon

    def _update_learning_parameters(self):
        """Update learning parameters (epsilon, learning rate, priority beta)"""
        # Decay epsilon
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay

        # Update learning rate with scheduling
        if self.use_lr_scheduling and self.episodes_trained % self.lr_decay_interval == 0:
            self.learning_rate = max(self.lr_min, self.learning_rate * self.lr_decay_rate)

        # Update priority beta (annealing)
        if self.use_prioritized_replay:
            progress = min(1.0, self.episodes_trained / 1000.0)  # Assume 1000 episodes for full annealing
            self.priority_beta = self.config.PRIORITY_BETA_START + progress * (
                self.priority_beta_end - self.config.PRIORITY_BETA_START
            )

        self.episodes_trained += 1

    def save(self, path: str):
        """Enhanced save with version compatibility"""
        save_data = {
            'version': self.config.MODEL_VERSION,
            'q_table': self.q_table,
            'memory': list(self.memory),
            'last_actions': list(self.last_actions),
            'epsilon': self.epsilon,
            'learning_rate': self.learning_rate,
            'episodes_trained': self.episodes_trained,
            'use_enhanced_state': self.use_enhanced_state,
            'training_metrics': self.training_metrics
        }

        if self.use_prioritized_replay:
            save_data['priorities'] = list(self.priorities)
            save_data['priority_beta'] = self.priority_beta

        # Create backup if auto backup is enabled
        if self.config.AUTO_BACKUP and os.path.exists(path):
            backup_path = path.replace('.npy', f'_backup_{self.episodes_trained}.npy')
            if os.path.exists(path):
                os.rename(path, backup_path)

        np.save(path, save_data)
        logging.info(f"Model saved to {path} (version {self.config.MODEL_VERSION})")

    def load(self, path: str):
        """Enhanced load with version compatibility"""
        if not os.path.exists(path):
            logging.warning(f"Model file {path} not found")
            return False

        try:
            data = np.load(path, allow_pickle=True).item()

            # Check version compatibility
            model_version = data.get('version', 'v1')
            if self.config.MODEL_COMPATIBILITY_CHECK and model_version != self.config.MODEL_VERSION:
                logging.warning(f"Loading model version {model_version}, current version is {self.config.MODEL_VERSION}")

            # Load Q-table with shape compatibility check
            loaded_q_table = data['q_table']
            if loaded_q_table.shape != self.q_table.shape:
                logging.warning(f"Q-table shape mismatch: {loaded_q_table.shape} vs {self.q_table.shape}")

                # Handle different model types
                if self.use_enhanced_state and loaded_q_table.shape == (10, 10, 4, 3, 3):
                    # Loading legacy model into enhanced agent - start fresh
                    logging.info("Legacy model detected, starting with fresh enhanced Q-table")
                elif not self.use_enhanced_state and len(loaded_q_table.shape) > 5:
                    # Loading enhanced model into legacy agent - start fresh
                    logging.info("Enhanced model detected but enhanced state disabled, starting with fresh legacy Q-table")
                else:
                    # Try to load anyway (might work for minor shape differences)
                    try:
                        self.q_table = loaded_q_table
                        logging.info("Q-table loaded despite shape mismatch")
                    except Exception as e:
                        logging.warning(f"Could not load Q-table: {e}, starting fresh")
            else:
                self.q_table = loaded_q_table

            # Load other data
            self.memory = deque(data.get('memory', []), maxlen=self.config.MEMORY_SIZE)
            self.last_actions = deque(data.get('last_actions', []), maxlen=10)
            self.epsilon = data.get('epsilon', self.config.EPSILON_START)
            self.learning_rate = data.get('learning_rate', self.config.LEARNING_RATE)
            self.episodes_trained = data.get('episodes_trained', 0)

            # Update enhanced state setting based on loaded model
            loaded_enhanced_state = data.get('use_enhanced_state', False)
            if loaded_enhanced_state != self.use_enhanced_state:
                logging.info(f"Model enhanced state: {loaded_enhanced_state}, current config: {self.use_enhanced_state}")
                if loaded_enhanced_state and not self.use_enhanced_state:
                    logging.warning("Loaded enhanced model but enhanced state is disabled in config")

            # Load prioritized replay data
            if self.use_prioritized_replay and 'priorities' in data:
                self.priorities = deque(data['priorities'], maxlen=self.config.MEMORY_SIZE)
                self.priority_beta = data.get('priority_beta', self.config.PRIORITY_BETA_START)

            # Load training metrics
            if 'training_metrics' in data:
                self.training_metrics = data['training_metrics']

            logging.info(f"Model loaded from {path} (version {model_version}, {len(self.memory)} experiences)")
            return True

        except Exception as e:
            logging.error(f"Error loading model from {path}: {e}")
            return False
