#!/usr/bin/env python3
"""
Test web app functionality
"""

def test_imports():
    """Test all imports"""
    try:
        print("Testing imports...")
        
        import config
        print("✓ Config imported")
        
        import snake_game
        print("✓ SnakeGame imported")
        
        import agent
        print("✓ Agent imported")
        
        from agent import RLAgent
        print("✓ RLAgent imported")
        
        from snake_game import SnakeGame
        print("✓ SnakeGame class imported")
        
        return True
    except Exception as e:
        print(f"✗ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_agent_creation():
    """Test agent creation"""
    try:
        print("\nTesting agent creation...")
        from agent import RLAgent
        
        agent = RLAgent(state_size=(10, 10), action_size=3)
        print(f"✓ Agent created with Q-table shape: {agent.q_table.shape}")
        return True
    except Exception as e:
        print(f"✗ Agent creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_loading():
    """Test model loading"""
    try:
        print("\nTesting model loading...")
        from agent import RLAgent
        import os
        
        agent = RLAgent(state_size=(10, 10), action_size=3)
        
        if os.path.exists("models/snake_agent_v2.npy"):
            success = agent.load("models/snake_agent_v2.npy")
            if success:
                print(f"✓ Model loaded successfully")
                print(f"  Q-table shape: {agent.q_table.shape}")
                print(f"  Memory size: {len(agent.memory)}")
                return True
            else:
                print("✗ Model loading failed")
                return False
        else:
            print("! No model file found")
            return True
    except Exception as e:
        print(f"✗ Model loading test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_web_trainer():
    """Test WebTrainer creation"""
    try:
        print("\nTesting WebTrainer...")
        import sys
        sys.path.append('web')
        
        from app import WebTrainer
        trainer = WebTrainer()
        print(f"✓ WebTrainer created")
        print(f"  Model path: {trainer.model_path}")
        return True
    except Exception as e:
        print(f"✗ WebTrainer creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== Testing Snake RL Web Components ===")
    
    tests = [
        ("Imports", test_imports),
        ("Agent Creation", test_agent_creation),
        ("Model Loading", test_model_loading),
        ("WebTrainer", test_web_trainer),
    ]
    
    passed = 0
    for name, test_func in tests:
        print(f"\n--- {name} ---")
        if test_func():
            passed += 1
        else:
            print(f"Stopping tests due to {name} failure")
            break
    
    print(f"\n=== Results: {passed}/{len(tests)} tests passed ===")
    
    if passed == len(tests):
        print("✅ All tests passed! Web app should work.")
    else:
        print("❌ Some tests failed. Check the errors above.")
